{{ define "main" }}
<!-- Hero Section -->
<section class="hero" id="hero">
  <img src="/img/hero/bubbles.png" alt="Bubbles" class="hero-bubbles">

  <div class="hero-content">
    <div class="hero-title">
      <img src="/img/hero/title.png" alt="ZEUS">
    </div>

    <div class="hero-characters">
      <div class="hero-character">
        <img src="/img/hero/01.png" alt="Zeus Character 1">
      </div>
      <div class="hero-character">
        <img src="/img/hero/01-2.png" alt="Zeus Character 2">
      </div>
      <div class="hero-character">
        <img src="/img/hero/01-3.png" alt="Zeus Character 3">
      </div>
    </div>

    <div class="hero-actions">
      <a href="{{ .Site.Params.social.dexscreener }}" target="_blank" rel="noopener" class="buy-btn">
        <img src="/img/btn.svg" alt="BUY NOW">
      </a>
    </div>

    <div class="social-icons">
      <a href="{{ .Site.Params.social.twitter }}" target="_blank" rel="noopener">
        <img src="/img/x.svg" alt="Twitter">
      </a>
      <a href="{{ .Site.Params.social.telegram }}" target="_blank" rel="noopener">
        <img src="/img/t.svg" alt="Telegram">
      </a>
    </div>
  </div>

  <img src="/img/hero/bottom.png" alt="Decor" class="hero-bottom">
</section>

<!-- About Section -->
<section class="about" id="about">
  <div class="about-container">
    <div class="about-image">
      <img src="/img/about/WANTED.png" alt="Wanted Poster">
    </div>

    <div class="about-content">
      <div class="about-title">
        <img src="/img/about/Title.png" alt="About Zeus">
      </div>

      <div class="about-description">
        {{ .Site.Params.description }} $ZEUS is more than just a meme coin. It's divine chaos, unstoppable energy, and pure fun, powered by the god of thunder himself. Forged on Ethereum. Destined for greatness. Welcome to the era of $ZEUS.
      </div>

      <div class="about-actions">
        <a href="{{ .Site.Params.social.dexscreener }}" target="_blank" rel="noopener" class="buy-btn">
          BUY NOW
        </a>
        <div class="social-icons">
          <a href="{{ .Site.Params.social.twitter }}" target="_blank" rel="noopener">
            <img src="/img/x.svg" alt="Twitter">
          </a>
          <a href="{{ .Site.Params.social.telegram }}" target="_blank" rel="noopener">
            <img src="/img/t.svg" alt="Telegram">
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- How to Buy Section -->
<section class="how-to-buy" id="how-to-buy">
  <div class="how-container">
    <div class="how-images">
      <div class="how-image">
        <img src="/img/how/01.png" alt="How to Buy Image 1">
      </div>
      <div class="how-image">
        <img src="/img/how/02.png" alt="How to Buy Image 2">
      </div>
    </div>

    <div class="how-title">
      <img src="/img/how/title.png" alt="How to Buy">
    </div>

    <div class="how-steps">
      <div class="how-step">
        <h3>Set up your wallet</h3>
        <p>Download MetaMask or another Ethereum-compatible wallet. Make sure it's connected to the Ethereum network.</p>
      </div>

      <div class="how-step">
        <h3>Get some ETH</h3>
        <p>Buy Ethereum on a centralized exchange (like Coinbase, Bybit or Binance) and transfer it to your wallet.</p>
      </div>

      <div class="how-step">
        <h3>Go to Uniswap</h3>
        <p>Head over to Uniswap and connect your wallet. Paste the official $ZEUS contract address.</p>
      </div>

      <div class="how-step">
        <h3>Swap ETH for $ZEUS</h3>
        <p>Enter the amount of ETH and confirm the transaction. Now you're holding $ZEUS.</p>
      </div>
    </div>
  </div>
</section>

<!-- Tokenomics Section -->
<section class="tokenomics" id="tokenomics">
  <div class="tokenomics-container">
    <div class="tokenomics-title">
      <img src="/img/tokenomics/title.png" alt="Tokenomics">
    </div>

    <div class="tokenomics-content">
      <div class="tokenomics-image">
        <img src="/img/tokenomics/01.png" alt="Tokenomics Background">
      </div>

      <div class="tokenomics-info">
        <div class="token-supply">{{ .Site.Params.total_supply }}</div>
        <div class="contract-address">{{ .Site.Params.contract_address }}</div>
      </div>
    </div>

    <div class="tokenomics-bottom">
      <img src="/img/hero/bottom.png" alt="Decor">
    </div>
  </div>
</section>

<!-- Join Section -->
<section class="join" id="join">
  <div class="join-container">
    <div class="join-social">
      <a href="{{ .Site.Params.social.twitter }}" target="_blank" rel="noopener">
        <img src="/img/x.svg" alt="Twitter">
      </a>
      <a href="{{ .Site.Params.social.telegram }}" target="_blank" rel="noopener">
        <img src="/img/t.svg" alt="Telegram">
      </a>
    </div>

    <div class="join-buy">
      <a href="{{ .Site.Params.social.dexscreener }}" target="_blank" rel="noopener" class="buy-btn">
        <img src="/img/btn.svg" alt="BUY NOW">
      </a>
    </div>
  </div>
</section>
{{ end }}
