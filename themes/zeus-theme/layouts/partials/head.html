<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>{{ if .IsHome }}{{ site.Title }}{{ else }}{{ printf "%s | %s" .Title site.Title }}{{ end }}</title>
<meta name="description" content="{{ .Site.Params.description }}">
<meta name="keywords" content="Zeus, cryptocurrency, meme coin, Ethereum, blockchain, DeFi">
<meta name="author" content="Zeus Team">

<!-- Open Graph / Facebook -->
<meta property="og:type" content="website">
<meta property="og:url" content="{{ .Permalink }}">
<meta property="og:title" content="{{ .Site.Title }}">
<meta property="og:description" content="{{ .Site.Params.description }}">
<meta property="og:image" content="{{ .Site.BaseURL }}img/logo.png">

<!-- Twitter -->
<meta property="twitter:card" content="summary_large_image">
<meta property="twitter:url" content="{{ .Permalink }}">
<meta property="twitter:title" content="{{ .Site.Title }}">
<meta property="twitter:description" content="{{ .Site.Params.description }}">
<meta property="twitter:image" content="{{ .Site.BaseURL }}img/logo.png">

<link rel="icon" type="image/x-icon" href="/favicon.ico">

{{ partialCached "head/css.html" . }}
{{ partialCached "head/js.html" . }}
