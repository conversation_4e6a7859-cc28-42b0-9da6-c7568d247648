/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: #000;
  color: #fff;
  overflow-x: hidden;
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
}

/* Header styles */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo img {
  height: 40px;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  color: #fff;
  font-weight: bold;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.nav-menu a:hover {
  color: #ffd700;
}

.header-social {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.header-social a img {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.header-social a:hover img {
  transform: scale(1.1);
}

.buy-btn-header {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: bold;
  text-transform: uppercase;
  transition: transform 0.3s ease;
}

.buy-btn-header:hover {
  transform: scale(1.05);
}

/* Main content */
main {
  margin-top: 80px;
}

/* Hero section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  background: radial-gradient(circle at center, #1a1a2e 0%, #000 100%);
  overflow: hidden;
}

.hero-bubbles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  padding: 2rem;
}

.hero-title {
  margin-bottom: 2rem;
  animation: float 3s ease-in-out infinite;
}

.hero-characters {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.hero-character {
  animation: bounce 2s ease-in-out infinite;
}

.hero-character:nth-child(2) {
  animation-delay: 0.3s;
}

.hero-character:nth-child(3) {
  animation-delay: 0.6s;
}

.hero-actions {
  margin: 2rem 0;
}

.buy-btn {
  display: inline-block;
  margin: 1rem;
  transition: transform 0.3s ease;
}

.buy-btn:hover {
  transform: scale(1.05);
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

.social-icons a {
  transition: transform 0.3s ease;
}

.social-icons a:hover {
  transform: scale(1.1);
}

.hero-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 1;
}

/* About section */
.about {
  position: relative;
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #000 100%);
  text-align: center;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-image {
  animation: float 4s ease-in-out infinite;
}

.about-content {
  text-align: left;
}

.about-title {
  margin-bottom: 2rem;
}

.about-description {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 2rem;
  color: #ccc;
}

.about-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

/* How to Buy section */
.how-to-buy {
  position: relative;
  padding: 4rem 2rem;
  background-image: url('/img/how/BG.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.how-to-buy::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1;
}

.how-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.how-images {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.how-image {
  animation: bounce 3s ease-in-out infinite;
}

.how-image:nth-child(2) {
  animation-delay: 0.5s;
}

.how-title {
  margin: 2rem 0;
}

.how-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.how-step {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem;
  border: 1px solid rgba(255, 215, 0, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.how-step:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.how-step h3 {
  color: #ffd700;
  font-size: 1.3rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.how-step p {
  color: #ccc;
  line-height: 1.6;
}

/* Tokenomics section */
.tokenomics {
  position: relative;
  padding: 4rem 2rem;
  background: radial-gradient(circle at center, #1a1a2e 0%, #000 100%);
  text-align: center;
}

.tokenomics-container {
  max-width: 1200px;
  margin: 0 auto;
}

.tokenomics-title {
  margin-bottom: 3rem;
}

.tokenomics-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4rem;
  flex-wrap: wrap;
}

.tokenomics-image {
  animation: rotate 20s linear infinite;
}

.tokenomics-info {
  text-align: left;
}

.token-supply {
  font-size: 3rem;
  font-weight: bold;
  color: #ffd700;
  margin-bottom: 1rem;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.contract-address {
  font-family: 'Courier New', monospace;
  font-size: 1.2rem;
  color: #ccc;
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 10px;
  border: 1px solid rgba(255, 215, 0, 0.3);
  word-break: break-all;
}

.tokenomics-bottom {
  margin-top: 3rem;
}

/* Join section */
.join {
  position: relative;
  padding: 4rem 2rem;
  background-image: url('/img/join/BG.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  text-align: center;
}

.join::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1;
}

.join-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
}

.join-social {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.join-social a {
  transition: transform 0.3s ease;
}

.join-social a:hover {
  transform: scale(1.1);
}

.join-buy {
  margin: 2rem 0;
}

/* Footer */
footer {
  background: #000;
  padding: 3rem 2rem 1rem;
  border-top: 1px solid #333;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer-section h3 {
  color: #ffd700;
  margin-bottom: 1rem;
  text-transform: uppercase;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-nav a {
  color: #ccc;
  transition: color 0.3s ease;
}

.footer-nav a:hover {
  color: #ffd700;
}

.footer-social {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.footer-social a {
  transition: transform 0.3s ease;
}

.footer-social a:hover {
  transform: scale(1.1);
}

.footer-social img {
  width: 32px;
  height: 32px;
}

.footer-bottom {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #333;
  color: #666;
}

.footer-bottom a {
  color: #ffd700;
  transition: color 0.3s ease;
}

.footer-bottom a:hover {
  color: #fff;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(-15px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    gap: 1rem;
  }

  .about-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .about-content {
    text-align: center;
  }

  .how-steps {
    grid-template-columns: 1fr;
  }

  .tokenomics-content {
    flex-direction: column;
  }

  .token-supply {
    font-size: 2rem;
  }

  .hero-characters {
    gap: 1rem;
  }

  .hero-character img {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 1rem;
  }

  .about, .how-to-buy, .tokenomics, .join {
    padding: 2rem 1rem;
  }

  .token-supply {
    font-size: 1.5rem;
  }

  .contract-address {
    font-size: 0.9rem;
  }
}

/* Additional enhancements */
.contract-address {
  cursor: pointer;
  transition: all 0.3s ease;
}

.contract-address:hover {
  background: rgba(255, 215, 0, 0.2);
  border-color: #ffd700;
}

/* Loading animation */
body:not(.loaded) {
  overflow: hidden;
}

body:not(.loaded)::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Scroll indicator */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  z-index: 1001;
  transition: width 0.3s ease;
}

/* Enhanced hover effects */
.buy-btn img {
  transition: filter 0.3s ease;
}

.buy-btn:hover img {
  filter: brightness(1.1) drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

/* Glowing text effect */
.token-supply {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6);
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Fix for navigation menu styling */
.nav-menu ul {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 0.25rem;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: #fff;
  transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile menu styles */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: flex;
    order: 3;
  }

  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-menu ul {
    flex-direction: column;
    padding: 2rem;
    gap: 1rem;
  }

  .header-social {
    order: 2;
  }
}
