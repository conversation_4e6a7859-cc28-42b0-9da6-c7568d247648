# ZEUS - True Zeus Coin Website

This is an exact replica of the [truezeuscoin.com](https://www.truezeuscoin.com) website built with Hugo static site generator.

## Features

- **Exact Design Replica**: Pixel-perfect recreation of the original website
- **Responsive Design**: Fully responsive layout that works on all devices
- **Smooth Animations**: CSS animations and transitions matching the original
- **Interactive Elements**: 
  - Smooth scrolling navigation
  - Animated characters and elements
  - Hover effects and transitions
  - Mobile-friendly hamburger menu
  - Scroll progress indicator
  - Clickable contract address (copies to clipboard)

## Structure

```
zeus/
├── content/
│   └── _index.md              # Homepage content
├── themes/zeus-theme/
│   ├── assets/
│   │   ├── css/main.css       # All styling
│   │   └── js/main.js         # Interactive functionality
│   ├── layouts/
│   │   ├── _default/
│   │   │   ├── baseof.html    # Base template
│   │   │   └── home.html      # Homepage layout
│   │   └── partials/
│   │       ├── head.html      # HTML head section
│   │       ├── header.html    # Site header
│   │       └── footer.html    # Site footer
│   └── static/
│       └── img/               # All images from original site
├── static/
│   └── favicon.ico
└── hugo.toml                  # Site configuration
```

## Sections Implemented

1. **Hero Section**: Large title, animated characters, buy button
2. **About Section**: "Wanted" poster style with description
3. **How to Buy**: Step-by-step guide with background image
4. **Tokenomics**: Token supply and contract address
5. **Join Section**: Social links and final call-to-action
6. **Footer**: Navigation and social links

## Assets

All original images have been downloaded and organized:
- Logo and branding elements
- Hero section characters and backgrounds
- Section titles and decorative elements
- Social media icons
- Background images

## Configuration

The site is configured in `hugo.toml` with:
- Site metadata
- Social media links
- Contract address
- Token supply information

## Development

To run the development server:

```bash
hugo server --bind 0.0.0.0 --port 1313
```

The site will be available at `http://localhost:1313`

## Build

To build for production:

```bash
hugo --minify
```

The built site will be in the `public/` directory.

## Technologies Used

- **Hugo**: Static site generator
- **CSS3**: Modern CSS with animations, gradients, and responsive design
- **JavaScript**: Vanilla JS for interactivity
- **HTML5**: Semantic markup

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design for all screen sizes
