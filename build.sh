#!/bin/bash

# ZEUS Website Build Script

echo "🔨 Building ZEUS website..."

# Clean previous build
if [ -d "public" ]; then
    rm -rf public
    echo "✅ Cleaned previous build"
fi

# Build the site
hugo --minify

if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 Files are ready in the 'public' directory"
    echo "🚀 You can now deploy the contents of 'public' to your web server"
else
    echo "❌ Build failed!"
    exit 1
fi
